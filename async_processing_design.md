## 1. 処理概要

BCMonitoringシステムは、Ethereumブロックチェーンのリアルタイム監視において、以下の非同期処理パターンを採用している：

- **Producer-Consumer パターン**  
  Web3j WebSocketからの新規ブロック通知を非同期で受信し、BlockingQueueを介してデータを受け渡し
- **並行処理**  
  Pending Transaction処理と新規ブロック監視を独立して実行

## 2. 非同期処理が必要な理由

### 2.1 イベント監視の連続性保証

- **連続性**: ブロックチェーンは2秒間隔で新規ブロックを生成
- **不可逆性**: 一度生成されたブロックは取り消せず、監視の中断はデータ欠損を意味する

### 2.2 Pending Transaction処理中の新規ブロック監視継続

- **Pending Transaction処理**: 過去ブロックの未処理トランザクションを順次処理（時間がかかる）
- **新規ブロック監視**: リアルタイムで発生する新規ブロックの即座処理（優先度高）

**非同期処理による解決例:**

```java
// 同期的実装の問題例（アンチパターン）
Pending Transaction処理  // 長時間実行
新規ブロック監視 // Pending Transaction処理中にたくさんのブロックが生成され、新規ブロックを見逃す可能性

// 非同期実装による解決
Pending Transaction処理中に新規ブロック監視を先に、別のThreadで実行し、新規ブロックのTransactionをqueueに累積していく
```


## 3. 処理フロー

### 3.1 実際の処理順序

BCMonitoringの非同期処理は以下の順序で実行される：

```java
// MonitorEventService.monitorEvents()の実際の処理順序
BlockingQueue<Transaction> transactionsQueue = eventLogRepository.subscribe();  // 1. WebSocket購読開始
List<Transaction> pendingTransactionsQueue = eventLogRepository.getFilterLogs(blockNumber + 1);  // 2. 過去ブロック取得

processPendingTransactions(pendingTransactionsQueue);  // 3. 過去ブロック処理（同期）
processNewTransactions(transactionsQueue);             // 4. 新規ブロック処理（非同期キューから取得）
```

### 3.2 非同期処理のポイント

1. **WebSocket購読の非同期性**
   `eventLogRepository.subscribe()`実行時点で、Web3jのWebSocketが非同期でブロック通知を受信開始し、BlockingQueueに蓄積

2. **処理の順序性**
   - `processPendingTransactions()`: 同期的に過去ブロックを順次処理
   - `processNewTransactions()`: 非同期的に蓄積されたキューから新規ブロックを処理

3. **並行性の実現**
   過去ブロック処理中も、WebSocketコールバックが並行してキューにデータを蓄積し続ける

## 4. エラーハンドリング

| エラー種別         | 発生箇所           | 対応方法             | リカバリ戦略     |
| ------------------ | ------------------ | -------------------- | ---------------- |
| WebSocket切断      | Web3j Subscribe    | エラーTransaction生成 | 自動再接続       |
| ブロック処理エラー | 非同期コールバック | blockNumber=-1で通知 | 自動再接続       |
| キュー満杯         | BlockingQueueにPut |                      |                  |

## 5. 性能特性と制約

### 5.1 性能指標

- **メモリ使用量**: BlockingQueue上限 = Integer.MAX_VALUE

### 5.2 スケーラビリティ制約

- **水平スケール不可**: 順次に処理する必要があるため、水平スケールできない