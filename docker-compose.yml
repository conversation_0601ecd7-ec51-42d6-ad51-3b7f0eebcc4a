version: '3.8'

services:
  bcmonitoring:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "2345:2345"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    environment:
      - WEBSOCKET_URI_HOST=host.docker.internal
      - WEBSOCKET_URI_PORT=18541
      - DYNAMODB_ENDPOINT=http://localstack:4566
      - DYNAMODB_REGION=ap-northeast-1
      - DYNAMODB_TABLE_NAME_PREFIX=local
      - S3_BUCKET_NAME=abijson-local-bucket
      - LOCALSTACK_ENDPOINT=http://localstack:4566
      - S3_REGION=ap-northeast-1
      - AWS_ACCESS_KEY=dummy
      - AWS_SECRET_KEY=dummy
      - SUBSCRIPTION_CHECK_INTERVAL=3000
      - ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC=2
      - EVENTS_TABLE_NAME=Events
      - BLOCK_HEIGHT_TABLE_NAME=BlockHeight
      - ENV=local
      - ABI_FORMAT=hardhat
      - ZONE_ID=3000
    depends_on:
      localstack:
        condition: service_healthy

  localstack:
    image: localstack/localstack:3.0.2
    environment:
      - SERVICES=s3,dynamodb
      - DEFAULT_REGION=ap-northeast-1
      - LS_LOG=warn # localstackへのリクエストログがアプリログを圧迫するためwarn以上を出力する
    ports:
      - "4566-4599:4566-4599"
    volumes:
      - ./docker/local/init-localstack.sh:/etc/localstack/init/ready.d/init-aws.sh
      - ./docker/local/data/s3:/tmp/data
    healthcheck: # https://github.com/localstack/localstack/issues/732#issuecomment-**********
      test: curl http://localhost:4566 || exit 1
      interval: 20s
      retries: 5
      start_period: 20s
      timeout: 10s
