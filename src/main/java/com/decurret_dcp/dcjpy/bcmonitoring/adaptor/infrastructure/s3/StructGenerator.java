package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.dynamic.loading.ClassLoadingStrategy;
import net.bytebuddy.implementation.MethodCall;
import org.web3j.abi.datatypes.*;

/** StructGenerator */
public class StructGenerator {

  /**
   * Generate struct class from field types using ByteBuddy
   *
   * @param fieldTypes field types
   * @return the Dynamic Struct or Static Struct class
   * @throws Exception exception if any error occurs
   */
  public static Class<? extends Type<?>> generateStructClass(
      List<Class<? extends Type<?>>> fieldTypes) throws Exception {

    ByteBuddy byteBuddy = new ByteBuddy();

    // Define constructor parameter types
    Class<?>[] paramTypes = fieldTypes.toArray(new Class<?>[0]);
    Class<? extends Type<?>> structType =
        fieldTypes.stream().anyMatch(StructGenerator::isDynamic)
            ? DynamicStruct.class
            : StaticStruct.class;

    DynamicType.Builder<? extends Type<?>> structClass =
        byteBuddy
            .subclass(structType)
            .name(
                "com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3."
                    + structType.getSimpleName()
                    + "_"
                    + UUID.randomUUID().toString().replace("-", "_"));

    // Define fields
    for (int i = 0; i < fieldTypes.size(); i++) {
      structClass = structClass.defineField("field" + i, fieldTypes.get(i), Modifier.PUBLIC);
    }

    return structClass
        .defineConstructor(Modifier.PUBLIC)
        .withParameters(paramTypes)
        .intercept(
            MethodCall.invoke(structType.getConstructor(List.class))
                .withMethodCall(
                    MethodCall.invoke(Arrays.class.getMethod("asList", Object[].class))
                        .withArgumentArray()))
        .make()
        .load(structType.getClassLoader(), ClassLoadingStrategy.Default.INJECTION)
        .getLoaded();
  }

  // reference from TypeDecoder#isDynamic
  public static boolean isDynamic(Class<?> parameter) {
    return DynamicBytes.class.isAssignableFrom(parameter)
        || Utf8String.class.isAssignableFrom(parameter)
        || DynamicArray.class.isAssignableFrom(parameter)
        || DynamicStruct.class.isAssignableFrom(parameter);
  }
}
