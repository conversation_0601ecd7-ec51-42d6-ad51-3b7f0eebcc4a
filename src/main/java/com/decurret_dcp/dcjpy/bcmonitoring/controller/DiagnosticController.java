package com.decurret_dcp.dcjpy.bcmonitoring.controller;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.ListBucketsRequest;
import software.amazon.awssdk.services.s3.model.ListBucketsResponse;

/** Controller for diagnostic endpoints to check system health and connectivity. */
@RestController
@RequestMapping("/api/v1/diagnostic")
public class DiagnosticController {

  private final S3Client s3Client;
  private final BcmonitoringConfigurationProperties configProperties;
  private final LoggingService logger;

  public DiagnosticController(
      S3Client s3Client,
      BcmonitoringConfigurationProperties configProperties,
      LoggingService logger) {
    this.s3Client = s3Client;
    this.configProperties = configProperties;
    this.logger = logger;
  }

  /**
   * Check S3 connectivity.
   *
   * @return ResponseEntity with connection status
   */
  @GetMapping("/s3-connectivity")
  public ResponseEntity<Map<String, Object>> checkS3Connectivity() {
    Map<String, Object> response = new HashMap<>();
    response.put("environment", configProperties.getEnv());

    if (configProperties.getEnv().equals("local")) {
      response.put("localstack_endpoint", configProperties.getLocalstack().getEndpoint());
      response.put("localstack_region", configProperties.getLocalstack().getRegion());
    } else {
      response.put("aws_region", configProperties.getAws().getRegion());
    }

    try {
      // Try to list buckets to check connectivity
      ListBucketsRequest request = ListBucketsRequest.builder().build();
      ListBucketsResponse listBucketsResponse = s3Client.listBuckets(request);

      response.put("status", "success");
      response.put("message", "Successfully connected to S3");
      response.put("buckets_count", listBucketsResponse.buckets().size());

      logger.info(
          "S3 connectivity check successful. Found {} buckets",
          listBucketsResponse.buckets().size());

      return ResponseEntity.ok(response);
    } catch (Exception e) {
      logger.error("S3 connectivity check failed", e);

      response.put("status", "error");
      response.put("message", "Failed to connect to S3: " + e.getMessage());
      response.put("exception_type", e.getClass().getName());

      return ResponseEntity.status(500).body(response);
    }
  }
}
