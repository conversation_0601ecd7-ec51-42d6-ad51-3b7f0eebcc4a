package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.net.URI;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

/**
 * DynamoDB Connection Pool Manager Implements connection pooling with Semaphore to limit max 10
 * concurrent connections - Connection reuse pool - Connection lifecycle management
 */
@Component
public class DynamoDBConnectionPool {

  private static final int MAX_CONNECTIONS = 10;
  private static final int CONNECTION_TIMEOUT_SECONDS = 30;

  private final Semaphore connectionSemaphore;
  private final ConcurrentLinkedQueue<DynamoDbClient> connectionPool;
  private final BcmonitoringConfigurationProperties configuration;
  private final LoggingService logger;

  public DynamoDBConnectionPool(
      BcmonitoringConfigurationProperties configuration, LoggingService logger) {
    this.connectionSemaphore = new Semaphore(MAX_CONNECTIONS, true);
    this.connectionPool = new ConcurrentLinkedQueue<>();
    this.configuration = configuration;
    this.logger = logger;
  }

  /**
   * Acquire a DynamoDB connection from the pool
   *
   * @return DynamoDbClient instance
   * @throws InterruptedException if thread is interrupted while waiting
   * @throws RuntimeException if unable to acquire connection within timeout
   */
  public DynamoDbClient acquireConnection() throws InterruptedException {
    // Acquire semaphore permit (similar to Go's sem <- struct{}{})
    boolean acquired = connectionSemaphore.tryAcquire(CONNECTION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
    if (!acquired) {
      throw new RuntimeException(
          "Failed to acquire connection within timeout: "
              + CONNECTION_TIMEOUT_SECONDS
              + " seconds");
    }

    try {
      // Try to get existing connection from pool (similar to Go's clientPool.Get())
      DynamoDbClient client = connectionPool.poll();

      if (client != null) {
        logger.debug("Reused existing DynamoDB connection from pool");
        return client;
      }

      // Create new connection if pool is empty
      client = createNewConnection();
      logger.debug("Created new DynamoDB connection");
      return client;

    } catch (Exception e) {
      // Release semaphore if connection creation fails
      connectionSemaphore.release();
      throw new RuntimeException("Failed to create DynamoDB connection", e);
    }
  }

  /**
   * Release a DynamoDB connection back to the pool
   *
   * @param client DynamoDbClient to release
   */
  public void releaseConnection(DynamoDbClient client) {
    if (client != null) {
      // Return connection to pool for reuse (similar to Go's clientPool.Put())
      connectionPool.offer(client);
      logger.debug("Returned DynamoDB connection to pool");
    }

    // Release semaphore permit (similar to Go's <-sem)
    connectionSemaphore.release();
  }

  /**
   * Create a new DynamoDB client connection
   *
   * @return new DynamoDbClient instance
   */
  private DynamoDbClient createNewConnection() {
    if (DCFConst.LOCAL.equals(configuration.getEnv())) {
      return DynamoDbClient.builder()
          .endpointOverride(URI.create(configuration.getLocalstack().getEndpoint()))
          .region(Region.of(configuration.getLocalstack().getRegion()))
          .credentialsProvider(DefaultCredentialsProvider.create())
          .build();
    } else {
      return DynamoDbClient.builder()
          .region(Region.of(configuration.getAws().getRegion()))
          .credentialsProvider(DefaultCredentialsProvider.create())
          .build();
    }
  }
}
