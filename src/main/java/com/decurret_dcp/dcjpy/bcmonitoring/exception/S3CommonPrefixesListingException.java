package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is an error listing common prefixes in S3. */
public class S3CommonPrefixesListingException extends BcmonitoringException {

  private static final String ERROR_CODE = "S3_COMMON_PREFIXES_LISTING_ERROR";

  /**
   * Constructs a new S3CommonPrefixesListingException with the specified detail message.
   *
   * @param message the detail message
   */
  public S3CommonPrefixesListingException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new S3CommonPrefixesListingException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public S3CommonPrefixesListingException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}
