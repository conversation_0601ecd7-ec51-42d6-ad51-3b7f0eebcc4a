package com.decurret_dcp.dcjpy.bcmonitoring.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;

/** Standard error response model for API responses. */
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
public class ErrorResponse {

  private final String errorCode;
  private final String message;

  public String getErrorCode() {
    return errorCode;
  }

  public String getMessage() {
    return message;
  }
}
