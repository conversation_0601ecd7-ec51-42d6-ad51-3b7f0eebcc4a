package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is an error interacting with the blockchain. */
public class BlockchainException extends BcmonitoringException {

  private static final String ERROR_CODE = "BLOCKCHAIN_ERROR";

  /**
   * Constructs a new BlockchainException with the specified detail message.
   *
   * @param message the detail message
   */
  public BlockchainException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new BlockchainException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public BlockchainException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}
