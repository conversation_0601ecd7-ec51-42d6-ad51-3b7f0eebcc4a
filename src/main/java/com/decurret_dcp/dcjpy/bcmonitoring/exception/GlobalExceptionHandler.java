package com.decurret_dcp.dcjpy.bcmonitoring.exception;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/**
 * Global exception handler for the application. This class handles all exceptions thrown by the
 * application and returns appropriate error responses.
 */
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

  private final LoggingService loggingService;

  public GlobalExceptionHandler(LoggingService loggingService) {
    this.loggingService = loggingService;
  }

  /**
   * Handles BcmonitoringException and its subclasses.
   *
   * @param ex the exception
   * @return a ResponseEntity with an ErrorResponse
   */
  @ExceptionHandler(BcmonitoringException.class)
  public ResponseEntity<ErrorResponse> handleBcmonitoringException(BcmonitoringException ex) {

    loggingService.error("BcmonitoringException: {}", ex.getMessage(), ex);

    ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode(), ex.getMessage());

    return new ResponseEntity<>(errorResponse, determineHttpStatus(ex));
  }

  /**
   * Handles all other exceptions not specifically handled by other methods.
   *
   * @param ex the exception
   * @return a ResponseEntity with an ErrorResponse
   */
  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {

    loggingService.error("Unhandled exception: {}", ex.getMessage(), ex);

    ErrorResponse errorResponse = new ErrorResponse("INTERNAL_SERVER_ERROR", ex.getMessage());

    return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  /**
   * Determines the appropriate HTTP status based on the exception type.
   *
   * @param ex the exception
   * @return the appropriate HTTP status
   */
  private HttpStatus determineHttpStatus(BcmonitoringException ex) {
    if (ex instanceof ResourceNotFoundException) {
      return HttpStatus.NOT_FOUND;
    } else if (ex instanceof DataAccessException
        || ex instanceof S3Exception
        || ex instanceof S3CommonPrefixesListingException) {
      return HttpStatus.INTERNAL_SERVER_ERROR;
    } else if (ex instanceof ConfigurationException) {
      return HttpStatus.INTERNAL_SERVER_ERROR;
    } else if (ex instanceof BlockchainException) {
      return HttpStatus.SERVICE_UNAVAILABLE;
    }

    return HttpStatus.INTERNAL_SERVER_ERROR;
  }
}
