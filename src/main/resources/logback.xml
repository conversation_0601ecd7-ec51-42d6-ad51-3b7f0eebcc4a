<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Common encoder properties for all appenders -->
    <property name="COMMON_ENCODER_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} [%X{correlationId}] - %msg%n" />

    <!-- Console Appender with Text Format (for development) -->
    <appender name="CONSOLE_TEXT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${COMMON_ENCODER_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Console Appender with JSON Format -->
    <appender name="CONSOLE_JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <!-- Include all MDC fields in the JSON output -->
            <includeMdc>true</includeMdc>
            <!-- Add application name -->
            <customFields>{"application":"bcmonitoring"}</customFields>
        </encoder>
    </appender>

    <!-- Rolling File Appender with Text Format -->
    <appender name="FILE_TEXT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/app.log</file>
        <encoder>
            <pattern>${COMMON_ENCODER_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/app.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Rolling File Appender with JSON Format -->
    <appender name="FILE_JSON" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/app.json</file>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <!-- Include all MDC fields in the JSON output -->
            <includeMdcKeyName>*</includeMdcKeyName>
            <!-- Add application name -->
            <customFields>{"application":"bcmonitoring"}</customFields>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/app.%d{yyyy-MM-dd}.json</fileNamePattern>
            <maxHistory>7</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Configure root logger -->
    <root level="INFO">
        <!-- Use JSON format for production, text format for development -->
        <appender-ref ref="CONSOLE_JSON" />
        <appender-ref ref="FILE_JSON" />
    </root>

    <!-- Application-specific logging -->
    <logger name="com.decurret_dcp.dcjpy" level="DEBUG" />

    <!-- Reduce the verbosity of common loggers -->
    <logger name="org.springframework" level="INFO" />
    <logger name="software.amazon.awssdk" level="INFO" />
</configuration>