{"address": "0xbd241Fee6334448B12C2Edc9C82C9E4169847a4C", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "accessCtrl", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "account", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "balanceSyncBridge", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "businessZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialCheck", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "financialZoneAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcApp", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "string", "name": "ibcAppName"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "ibcToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "issuer", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "provider", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "setContracts", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "contracts", "components": [{"type": "address", "name": "ctrlAddress"}, {"type": "address", "name": "providerAddress"}, {"type": "address", "name": "issuer<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "validator<PERSON><PERSON><PERSON>"}, {"type": "address", "name": "accountAddress"}, {"type": "address", "name": "financialZoneAccountAddress"}, {"type": "address", "name": "businessZoneAccountAddress"}, {"type": "address", "name": "tokenAddress"}, {"type": "address", "name": "ibc<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "address", "name": "financialCheckAddress"}, {"type": "address", "name": "transferProxyAddress"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setIbcApp", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "ibcAppAddress"}, {"type": "string", "name": "ibcAppName"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "token", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "transferProxy", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "validator", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x77099f400addc4e0917b98fa12c64f8959b9df0f04036bd43c93e878cb8d8ea3", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0xbd241Fee6334448B12C2Edc9C82C9E4169847a4C", "transactionIndex": 0, "gasUsed": "1388509", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x6664ed6b9d1411bbd959d62b049c9af6879ef38f0b98aab93197a2010e2ff5c3", "blockNumber": 110, "cumulativeGasUsed": "1388509", "status": 1}}