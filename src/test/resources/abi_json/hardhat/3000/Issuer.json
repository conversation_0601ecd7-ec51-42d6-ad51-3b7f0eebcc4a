{"address": "0xE85BB147c7132F22537E6FEB8De585FcdBBAe0E8", "abi": [{"type": "event", "anonymous": false, "name": "AddAccountBy<PERSON><PERSON>uer", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddBizZoneToIssuer", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": false}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON>d<PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "uint16", "name": "bankCode", "indexed": true}, {"type": "string", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddIssuerRole", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "address", "name": "issuerE<PERSON>", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "CumulativeReset", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}, {"type": "bytes32", "name": "validatorId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "DeleteBizZoneToIssuer", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": false}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "IssuerEnabled", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "issuerId", "indexed": true}, {"type": "string", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModTokenLimit", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": true}, {"type": "tuple", "name": "limitUpdates", "indexed": false, "components": [{"type": "bool", "name": "mint"}, {"type": "bool", "name": "burn"}, {"type": "bool", "name": "charge"}, {"type": "bool", "name": "discharge"}, {"type": "bool", "name": "transfer"}, {"type": "tuple", "name": "cumulative", "components": [{"type": "bool", "name": "total"}, {"type": "bool", "name": "mint"}, {"type": "bool", "name": "burn"}, {"type": "bool", "name": "charge"}, {"type": "bool", "name": "discharge"}, {"type": "bool", "name": "transfer"}]}]}, {"type": "tuple", "name": "limitValues", "indexed": false, "components": [{"type": "uint256", "name": "mint"}, {"type": "uint256", "name": "burn"}, {"type": "uint256", "name": "charge"}, {"type": "uint256", "name": "discharge"}, {"type": "uint256", "name": "transfer"}, {"type": "tuple", "name": "cumulative", "components": [{"type": "uint256", "name": "total"}, {"type": "uint256", "name": "mint"}, {"type": "uint256", "name": "burn"}, {"type": "uint256", "name": "charge"}, {"type": "uint256", "name": "discharge"}, {"type": "uint256", "name": "transfer"}]}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "ROLE_PREFIX_ISSUER", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addAccountId", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addAccountRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "address", "name": "accountEoa"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addBizZoneToIssuer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "add<PERSON><PERSON><PERSON>", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "uint16", "name": "bankCode"}, {"type": "string", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addIssuerRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkBurn", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkMint", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "cumulativeReset", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "deleteBizZoneToIssuer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "forceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "getAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32[]", "name": "inAccountIds"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "offset"}], "outputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}], "outputs": [{"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getIssuerAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "issuer", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}]}, {"type": "function", "name": "getIssuerCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getIssuerId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getIssuerList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "offset"}], "outputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "isFrozen", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "frozen"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "mod<PERSON><PERSON><PERSON>", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modTokenLimit", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "limitUpdates", "components": [{"type": "bool", "name": "mint"}, {"type": "bool", "name": "burn"}, {"type": "bool", "name": "charge"}, {"type": "bool", "name": "discharge"}, {"type": "bool", "name": "transfer"}, {"type": "tuple", "name": "cumulative", "components": [{"type": "bool", "name": "total"}, {"type": "bool", "name": "mint"}, {"type": "bool", "name": "burn"}, {"type": "bool", "name": "charge"}, {"type": "bool", "name": "discharge"}, {"type": "bool", "name": "transfer"}]}]}, {"type": "tuple", "name": "limitValues", "components": [{"type": "uint256", "name": "mint"}, {"type": "uint256", "name": "burn"}, {"type": "uint256", "name": "charge"}, {"type": "uint256", "name": "discharge"}, {"type": "uint256", "name": "transfer"}, {"type": "tuple", "name": "cumulative", "components": [{"type": "uint256", "name": "total"}, {"type": "uint256", "name": "mint"}, {"type": "uint256", "name": "burn"}, {"type": "uint256", "name": "charge"}, {"type": "uint256", "name": "discharge"}, {"type": "uint256", "name": "transfer"}]}]}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "partialForceBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "burnedAmount"}, {"type": "uint256", "name": "burnedBalance"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setAccountStatus", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setIssuerAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "issuer", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xe5d55dea3e5ff6b053907de86af5f74b4160ce9a9be5a47ded1b756384ae0442", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0xE85BB147c7132F22537E6FEB8De585FcdBBAe0E8", "transactionIndex": 0, "gasUsed": "6570672", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xda2e03ea0733dc8ad7695e02fa73f14621feffbd36d5f3eee08bafa1bdca2209", "blockNumber": 165, "cumulativeGasUsed": "6570672", "status": 1}}