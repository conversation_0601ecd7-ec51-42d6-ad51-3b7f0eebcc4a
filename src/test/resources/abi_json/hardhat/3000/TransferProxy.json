{"address": "0xbe8EFB7575Cb49d9eA2376488b0259cc8A289826", "abi": [{"type": "event", "anonymous": false, "name": "AddRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}, {"type": "uint256", "name": "position", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "DeleteRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "addRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}, {"type": "uint256", "name": "position"}], "outputs": []}, {"type": "function", "name": "clearRule", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "deleteRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": []}, {"type": "function", "name": "findAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address[]", "name": "rules"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}, {"type": "address", "name": "token"}], "outputs": []}, {"type": "function", "name": "isRegistered", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x5429b734065e049eb3af6ea9aaa49d12d2b24166aefc194ea66cfdfaad6afe0d", "receipt": {"to": null, "from": "0xCe76FFfB36975dF748314A3aaB5FDB0180A3eE24", "contractAddress": "0xbe8EFB7575Cb49d9eA2376488b0259cc8A289826", "transactionIndex": 0, "gasUsed": "1234482", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xfc03e8fafb8dc5c77baf384f20d27b74f1443e975c3f5490ff84ab8646f5fa33", "blockNumber": 233, "cumulativeGasUsed": "1234482", "status": 1}}