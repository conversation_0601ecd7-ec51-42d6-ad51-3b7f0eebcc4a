package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import java.io.Serializable
import java.math.BigInteger
import java.util.*
import org.web3j.abi.datatypes.*
import org.web3j.abi.datatypes.generated.Uint256
import spock.lang.Specification

class StructGeneratorSpec extends Specification {

    def "generateStructClass should create StaticStruct for static types"() {
        given: "A list of static field types"
        def fieldTypes = [
            Uint256.class,
            Address.class,
            Bool.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
        !DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct for dynamic types"() {
        given: "A list containing dynamic field types"
        def fieldTypes = [
            Uint256.class,
            Utf8String.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicBytes is present"() {
        given: "A list containing DynamicBytes"
        def fieldTypes = [
            Uint256.class,
            DynamicBytes.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicArray is present"() {
        given: "A list containing DynamicArray"
        def fieldTypes = [
            Uint256.class,
            DynamicArray.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicStruct is present"() {
        given: "A list containing DynamicStruct"
        def fieldTypes = [
            Uint256.class,
            DynamicStruct.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should handle empty field types list"() {
        given: "An empty list of field types"
        def fieldTypes = []

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should handle single field type"() {
        given: "A single field type"
        def fieldTypes = [Uint256.class]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create unique class names"() {
        given: "Same field types"
        def fieldTypes = [Uint256.class, Address.class]

        when: "Generating multiple struct classes"
        def structClass1 = StructGenerator.generateStructClass(fieldTypes)
        def structClass2 = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create different classes with unique names"
        structClass1 != null
        structClass2 != null
        structClass1 != structClass2
        structClass1.getName() != structClass2.getName()
    }

    def "isDynamic should return true for DynamicBytes"() {
        expect:
        StructGenerator.isDynamic(DynamicBytes.class) == true
    }

    def "isDynamic should return true for Utf8String"() {
        expect:
        StructGenerator.isDynamic(Utf8String.class) == true
    }

    def "isDynamic should return true for DynamicArray"() {
        expect:
        StructGenerator.isDynamic(DynamicArray.class) == true
    }

    def "isDynamic should return true for DynamicStruct"() {
        expect:
        StructGenerator.isDynamic(DynamicStruct.class) == true
    }

    def "isDynamic should return false for static types"() {
        expect:
        StructGenerator.isDynamic(Uint256.class) == false
        StructGenerator.isDynamic(Address.class) == false
        StructGenerator.isDynamic(Bool.class) == false
        StructGenerator.isDynamic(StaticStruct.class) == false
    }

    def "isDynamic should handle null parameter"() {
        when: "Calling isDynamic with null"
        def result = StructGenerator.isDynamic(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "generateStructClass should handle mixed static and dynamic types"() {
        given: "A mix of static and dynamic types"
        def fieldTypes = [
            Uint256.class,      // Static
            Address.class,      // Static
            Utf8String.class,   // Dynamic
            Bool.class          // Static
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass due to presence of dynamic type"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create constructor with correct parameters"() {
        given: "Field types"
        def fieldTypes = [Uint256.class, Address.class]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should have constructor with correct parameter types"
        structClass != null
        def constructors = structClass.getConstructors()
        constructors.length > 0

        def constructor = constructors.find { it.getParameterCount() == fieldTypes.size() }
        constructor != null
    }

    def "constructor should be callable for completeness"() {
        when: "Creating an instance of StructGenerator"
        def generator = new StructGenerator()

        then: "Should create instance successfully"
        generator != null
        generator instanceof StructGenerator
    }
}
