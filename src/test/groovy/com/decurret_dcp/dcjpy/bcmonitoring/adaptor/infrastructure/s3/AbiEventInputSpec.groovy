package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import spock.lang.Specification

class AbiEventInputSpec extends Specification {

    def "constructor with 3 parameters should set fields correctly"() {
        given: "Parameters for AbiEventInput"
        def name = "testParam"
        def type = "uint256"
        def indexed = true

        when: "Creating AbiEventInput with 3 parameters"
        def input = new AbiParser.AbiEventInput(name, type, indexed)

        then: "Should set fields correctly"
        input.getName() == name
        input.getType() == type
        input.isIndexed() == indexed
        input.getComponents() == null
    }

    def "constructor with 4 parameters should set fields correctly"() {
        given: "Parameters including components"
        def name = "testParam"
        def type = "tuple"
        def indexed = false
        def components = [
            new AbiParser.AbiEventInput("field1", "uint256", true),
            new AbiParser.AbiEventInput("field2", "address", false)
        ]

        when: "Creating AbiEventInput with 4 parameters"
        def input = new AbiParser.AbiEventInput(name, type, indexed, components)

        then: "Should set fields correctly"
        input.getName() == name
        input.getType() == type
        input.isIndexed() == indexed
        input.getComponents() != null
        input.getComponents().size() == 2
        input.getComponents()[0].getName() == "field1"
        input.getComponents()[1].getName() == "field2"
    }

    def "constructor should handle null components"() {
        given: "Parameters with null components"
        def name = "testParam"
        def type = "tuple"
        def indexed = false
        List<AbiParser.AbiEventInput> components = null

        when: "Creating AbiEventInput with null components"
        def input = new AbiParser.AbiEventInput(name, type, indexed, components)

        then: "Should handle null components gracefully"
        input.getName() == name
        input.getType() == type
        input.isIndexed() == indexed
        input.getComponents() == null
    }

    def "constructor should handle empty components list"() {
        given: "Parameters with empty components list"
        def name = "testParam"
        def type = "tuple"
        def indexed = false
        def components = []

        when: "Creating AbiEventInput with empty components"
        def input = new AbiParser.AbiEventInput(name, type, indexed, components)

        then: "Should handle empty components correctly"
        input.getName() == name
        input.getType() == type
        input.isIndexed() == indexed
        input.getComponents() != null
        input.getComponents().isEmpty()
    }

    def "isTuple should return true for tuple types"() {
        expect: "isTuple returns true for tuple types"
        new AbiParser.AbiEventInput("test", "tuple", false).isTuple() == true
        new AbiParser.AbiEventInput("test", "tuple[]", false).isTuple() == true
        new AbiParser.AbiEventInput("test", "tuple[5]", false).isTuple() == true
    }

    def "isTuple should return false for non-tuple types"() {
        expect: "isTuple returns false for non-tuple types"
        new AbiParser.AbiEventInput("test", "uint256", false).isTuple() == false
        new AbiParser.AbiEventInput("test", "address", false).isTuple() == false
        new AbiParser.AbiEventInput("test", "string", false).isTuple() == false
        new AbiParser.AbiEventInput("test", "bytes32", false).isTuple() == false
    }

    def "isTuple should handle null type"() {
        when: "Creating AbiEventInput with null type"
        def input = new AbiParser.AbiEventInput("test", null, false)

        then: "isTuple should return false for null type"
        input.isTuple() == false
    }

    def "isTuple should handle empty type"() {
        when: "Creating AbiEventInput with empty type"
        def input = new AbiParser.AbiEventInput("test", "", false)

        then: "isTuple should return false for empty type"
        input.isTuple() == false
    }

    def "getters should return correct values"() {
        given: "AbiEventInput with specific values"
        def name = "paramName"
        def type = "bytes32"
        def indexed = true
        def input = new AbiParser.AbiEventInput(name, type, indexed)

        expect: "Getters return correct values"
        input.getName() == name
        input.getType() == type
        input.isIndexed() == indexed
        input.getComponents() == null
    }

    def "should handle null name"() {
        when: "Creating AbiEventInput with null name"
        def input = new AbiParser.AbiEventInput(null, "uint256", false)

        then: "Should handle null name gracefully"
        input.getName() == null
        input.getType() == "uint256"
        input.isIndexed() == false
    }

    def "should handle null type in constructor"() {
        when: "Creating AbiEventInput with null type"
        def input = new AbiParser.AbiEventInput("test", null, true)

        then: "Should handle null type gracefully"
        input.getName() == "test"
        input.getType() == null
        input.isIndexed() == true
    }

    def "components should be immutable copy"() {
        given: "Mutable components list"
        def originalComponents = [
            new AbiParser.AbiEventInput("field1", "uint256", true)
        ]
        def input = new AbiParser.AbiEventInput("test", "tuple", false, originalComponents)

        when: "Modifying original components list"
        originalComponents.add(new AbiParser.AbiEventInput("field2", "address", false))

        then: "Input components should remain unchanged"
        input.getComponents().size() == 1
        input.getComponents()[0].getName() == "field1"
    }

    def "should handle complex nested components"() {
        given: "Nested components structure"
        def nestedComponents = [
            new AbiParser.AbiEventInput("nestedField", "uint256", false)
        ]
        def components = [
            new AbiParser.AbiEventInput("field1", "uint256", true),
            new AbiParser.AbiEventInput("field2", "tuple", false, nestedComponents)
        ]

        when: "Creating AbiEventInput with nested components"
        def input = new AbiParser.AbiEventInput("complexTuple", "tuple", false, components)

        then: "Should handle nested structure correctly"
        input.getName() == "complexTuple"
        input.getType() == "tuple"
        input.getComponents().size() == 2
        input.getComponents()[1].getComponents() != null
        input.getComponents()[1].getComponents().size() == 1
        input.getComponents()[1].getComponents()[0].getName() == "nestedField"
    }
}
