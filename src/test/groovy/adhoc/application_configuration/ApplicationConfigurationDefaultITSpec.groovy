package adhoc.application_configuration

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class ApplicationConfigurationDefaultITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successfully runs the application using default values when optional environment variables are missing
	 * Verifies that ENV variable values fall back to their defaults
	 * Expected: Service uses default values from application.properties and logs "Started bc monitoring"
	 */
	def "Should use default values when optional env variables are missing"() {
		given: "CommandLineRunner is available"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Application is started via the command line runner"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Application uses default values from application.properties"
		properties.getAws().getRegion() == "ap-northeast-1"
		properties.getAws().getAccessKeyId() == "dummy"
		properties.getAws().getSecretAccessKey() == "dummy"
		properties.getAws().getDynamodb().getRegion() == "ap-northeast-1"
		properties.getAws().getDynamodb().getEndpoint() == "http://localstack:4566"
		properties.getAws().getS3().getBucketName() == "abijson-local-bucket"
		properties.getWebsocket().getUri().getHost() == "localhost"
		properties.getWebsocket().getUri().getPort() == "18541"
		properties.getSubscription().getCheckInterval() == "3000"
		properties.getSubscription().getAllowableBlockTimestampDiffSec() == "2"
		properties.getEnv() == "local"
		properties.getAbiFormat() == "hardhat"

		and: "Application should still start monitoring"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
	}
}
